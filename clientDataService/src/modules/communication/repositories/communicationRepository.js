import CommunicationChannel from '../../../domain/models/communication/communicationChannelModel.js';
import ChatMessage from '../../../domain/models/communication/chatMessageModel.js';
import ChannelReadCursor from '../../../domain/models/communication/channelReadCursorModel.js';
import { UserModel } from '../../../domain/models/userModel.js'; // eslint-disable-line no-unused-vars

class CommunicationRepository {
  async createChannel(channelData) {
    const channel = new CommunicationChannel(channelData);
    return await channel.save();
  }

  async updateChannel(channelId, updateData) {
    return await CommunicationChannel.findByIdAndUpdate(
      channelId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
  }

  async deleteChannel(channelId) {
    return await CommunicationChannel.findByIdAndUpdate(
      channelId,
      { isInactive: true, updatedAt: new Date() },
      { new: true }
    );
  }

  async getChannelById(channelId) {
    return await CommunicationChannel.findById(channelId)
      .populate('members', 'name email role')
      .populate('ownerId', 'name email role');
  }

  async getChannelsByUser(userId, options = {}) {
    const {
      type = null,
      isInactive = false,
      limit = 50,
      offset = 0,
      sortBy = 'lastMessageAt',
      sortOrder = -1
    } = options;

    const query = {
      members: userId,
      isInactive
    };

    if (type) {
      query.type = type;
    }

    return await CommunicationChannel.find(query)
      .populate('members', 'name email role')
      .populate('ownerId', 'name email role')
      .sort({ [sortBy]: sortOrder })
      .limit(limit)
      .skip(offset);
  }

  async addMemberToChannel(channelId, userId) {
    return await CommunicationChannel.findByIdAndUpdate(
      channelId,
      { $addToSet: { members: userId }, updatedAt: new Date() },
      { new: true }
    );
  }

  async removeMemberFromChannel(channelId, userId) {
    return await CommunicationChannel.findByIdAndUpdate(
      channelId,
      { $pull: { members: userId }, updatedAt: new Date() },
      { new: true }
    );
  }

  async getChannelsByContext(contextFilter) {
    const query = { isInactive: false };
    
    if (contextFilter.contextType) {
      query.contextType = contextFilter.contextType;
    }
    
    if (contextFilter.contextId) {
      query.contextId = contextFilter.contextId;
    }

    return await CommunicationChannel.find(query)
      .populate('members', 'name email role')
      .populate('ownerId', 'name email role');
  }

  // Message methods
  async createMessage(messageData) {
    const message = new ChatMessage(messageData);
    const savedMessage = await message.save();

    await CommunicationChannel.findByIdAndUpdate(
      messageData.channelId,
      {
        $inc: { messageCount: 1 },
        lastMessageAt: new Date(),
        updatedAt: new Date()
      }
    );

    return savedMessage;
  }

  async updateMessage(messageId, updateData) {
    return await ChatMessage.findByIdAndUpdate(
      messageId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
  }

  async editMessage(messageId, updateData, userId) {
    // Get the current message to store in edit history
    const currentMessage = await ChatMessage.findById(messageId);
    if (!currentMessage) {
      throw new Error('Message not found');
    }

    // Update the message with new content
    const updatedMessage = await ChatMessage.findByIdAndUpdate(
      messageId,
      {
        text: updateData.text,
        htmlContent: updateData.htmlContent,
        plainTextContent: updateData.plainTextContent,
        metadata: updateData.metadata,
        isEdited: true,
        updatedAt: new Date(),
        $push: {
          editHistory: {
            text: currentMessage.text,
            htmlContent: currentMessage.htmlContent,
            editedAt: new Date()
          }
        }
      },
      { new: true, runValidators: true }
    ).populate('ownerId', 'name email role')
     .populate('replyTo', 'text htmlContent ownerId createdAt');

    return updatedMessage;
  }

  async deleteMessage(messageId) {
    return await ChatMessage.findByIdAndUpdate(
      messageId,
      { isDeleted: true, updatedAt: new Date() },
      { new: true }
    );
  }

  async getMessageById(messageId) {
    return await ChatMessage.findById(messageId)
      .populate('ownerId', 'name email role')
      .populate('replyTo', 'text htmlContent ownerId createdAt');
  }

  async getMessagesByChannel(channelId, options = {}) {
    const {
      limit = 50,
      offset = 0,
      sortBy = 'order',
      sortOrder = 1,
      includeDeleted = false
    } = options;

    const query = {
      channelId
    };

    if (!includeDeleted) {
      query.isDeleted = false;
    }

    return await ChatMessage.find(query)
      .populate('ownerId', 'name email role')
      .populate('replyTo', 'text htmlContent ownerId createdAt')
      .sort({ [sortBy]: sortOrder })
      .limit(limit)
      .skip(offset);
  }

  async getNextMessageOrder(channelId) {
    const lastMessage = await ChatMessage.findOne({ channelId })
      .sort({ order: -1 })
      .select('order');
    
    return lastMessage ? lastMessage.order + 1 : 1;
  }

  async markMessageAsViewed(messageId, userId) {
    const message = await ChatMessage.findById(messageId);
    if (message && !message.isViewedBy(userId)) {
      return await message.markAsViewed(userId);
    }
    return message;
  }

  async markChannelMessagesAsViewed(channelId, userId, fromDate = null) {
    const query = {
      channelId,
      ownerId: { $ne: userId },
      'viewed.userId': { $ne: userId },
      isDeleted: false
    };

    if (fromDate) {
      query.createdAt = { $gte: fromDate };
    }

    return await ChatMessage.updateMany(
      query,
      {
        $push: {
          viewed: {
            userId: userId,
            viewedAt: new Date()
          }
        }
      }
    );
  }

  async getUnreadMessagesInfo(userId, channelId = null) {
    const query = {
      ownerId: { $ne: userId },
      'viewed.userId': { $ne: userId },
      isDeleted: false
    };

    if (channelId) {
      query.channelId = channelId;
    }

    const firstUnreadMessage = await ChatMessage.findOne(query)
      .populate('ownerId', 'name email role')
      .populate('replyTo', 'text htmlContent ownerId createdAt')
      .sort({ order: 1 }); 

    const unreadCount = await ChatMessage.countDocuments(query);

    return {
      unreadCount,
      firstUnreadMessage,
      hasUnreadMessages: unreadCount > 0
    };
  }

  async getUnreadMessages(userId, channelId, options = {}) {
    const {
      limit = 50,
      offset = 0
    } = options;

    const query = {
      channelId,
      ownerId: { $ne: userId },
      'viewed.userId': { $ne: userId },
      isDeleted: false
    };

    return await ChatMessage.find(query)
      .populate('ownerId', 'name email role')
      .populate('replyTo', 'text htmlContent ownerId createdAt')
      .sort({ order: 1 })
      .limit(limit)
      .skip(offset);
  }

  // Cursor-based methods for efficient polling
  async getReadCursor(channelId, userId) {
    return await ChannelReadCursor.findOne({ channelId, userId });
  }

  async upsertReadCursor(channelId, userId, lastReadOrder) {
    const now = new Date();
    return await ChannelReadCursor.findOneAndUpdate(
      { channelId, userId },
      { $max: { lastReadOrder }, $set: { updatedAt: now } },
      { new: true, upsert: true }
    );
  }

  async countUnreadByChannelWithCursor(channelId, userId) {
    const cursor = await this.getReadCursor(channelId, userId);
    const lastOrder = cursor ? cursor.lastReadOrder : 0;
    return await ChatMessage.countDocuments({
      channelId,
      order: { $gt: lastOrder },
      ownerId: { $ne: userId },
      isDeleted: false
    });
  }

  async getMessagesAfterOrder(channelId, afterOrder, limit = 50) {
    const query = {
      channelId,
      order: { $gt: afterOrder },
      isDeleted: false
    };

    return await ChatMessage.find(query)
      .populate('ownerId', 'name email role')
      .populate('replyTo', 'text htmlContent ownerId createdAt')
      .sort({ order: 1 })
      .limit(Math.min(limit, 100));
  }
}

export default CommunicationRepository;
