import { updateChannel } from '../useCases/updateChannel.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    // Extract user context from authorizer
    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    // Extract channel ID from path parameters
    const { channelId } = event.pathParameters;
    
    if (!channelId) {
      return apiResponse(400, { error: 'Channel ID is required' });
    }

    let body;
    try {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      return apiResponse(400, { error: 'Invalid JSON in request body' });
    }

    const { 
      name,
      members,
      groupIds,
      contextType,
      contextId
    } = body;

    // Update channel using use case
    const result = await updateChannel({
      userId,
      role,
      channelId,
      name,
      members,
      groupIds,
      contextType,
      contextId
    });

    return apiResponse(200, { body: result });

  } catch (error) {
    console.error('Error in updateChannel handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
