import { getUnreadMessagesInfo } from '../useCases/getUnreadMessages.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const queryParams = event.queryStringParameters || {};
    const { channelId } = queryParams;

    // Call use case
    const result = await getUnreadMessagesInfo({
      userId,
      role,
      channelId
    });

    // Compute ETag from unreadCount and channelId
    const unreadCount = result?.data?.unreadCount ?? 0;
    const etag = `W/"unread-${channelId || 'all'}-${unreadCount}"`;
    const ifNoneMatch = event.headers?.['If-None-Match'] || event.headers?.['if-none-match'];
    if (ifNoneMatch && ifNoneMatch === etag) {
      return apiResponse(304, { header: { ETag: etag }, body: '' });
    }

    return apiResponse(200, { header: { ETag: etag }, body: result });

  } catch (error) {
    console.error('Error in getUnreadMessagesInfo handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { error: 'Internal server error' });
  }
}
