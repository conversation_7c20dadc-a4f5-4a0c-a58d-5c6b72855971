import { getLastSeenTimestamps } from '../useCases/getLastSeenTimestamps.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const { channelId } = event.pathParameters || {};

    if (!channelId) {
      return apiResponse(400, { error: 'Channel ID is required' });
    }

    // Get last seen timestamps using use case
    const result = await getLastSeenTimestamps({
      userId,
      role,
      channelId
    });

    return apiResponse(200, { body: result });

  } catch (error) {
    console.error('Error in getLastSeenTimestamps handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { error: 'Internal server error' });
  }
}
