import { createChannel } from '../useCases/createChannel.js';
import { ValidationError, AuthorizationError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    let body;
    try {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      return apiResponse(400, { error: 'Invalid JSON in request body' });
    }

    const { 
      name,
      members,
      groupIds,
      type,
      contextType,
      contextId
    } = body;

    // Create channel w use case
    const result = await createChannel({
      userId,
      role,
      name,
      members,
      groupIds,
      type,
      contextType,
      contextId
    });

    return apiResponse(201, { body: result });

  } catch (error) {
    console.error('Error in createChannel handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    return apiResponse(500, { 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
