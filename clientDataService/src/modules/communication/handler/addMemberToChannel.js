import { addMemberToChannel } from '../useCases/addMemberToChannel.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const { channelId } = event.pathParameters || {};

    if (!channelId) {
      return apiResponse(400, { error: 'Channel ID is required' });
    }

    let body;
    try {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } catch (error) {
      return apiResponse(400, { error: 'Invalid JSON in request body' });
    }

    const { userId: memberUserId } = body;

    if (!memberUserId) {
      return apiResponse(400, { error: 'Member userId is required in request body' });
    }

    // Add member w use case
    const result = await addMemberToChannel({
      userId,
      role,
      channelId,
      memberUserId
    });

    return apiResponse(200, { body: result });

  } catch (error) {
    console.error('Error in addMemberToChannel handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { error: 'Internal server error' });
  }
}
