import { getChannelMessages } from '../useCases/getChannelMessages.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    const { channelId } = event.pathParameters;
    
    if (!channelId) {
      return apiResponse(400, { error: 'Channel ID is required' });
    }

    // Extract params for pagination and filtering
    const queryParams = event.queryStringParameters || {};
    const {
      limit,
      offset,
      sortBy,
      sortOrder,
      includeDeleted,
      afterOrder
    } = queryParams;

    // Get channel messages using use case
    const result = await getChannelMessages({
      userId,
      role,
      channelId,
      limit,
      offset,
      sortBy,
      sortOrder,
      includeDeleted,
      afterOrder
    });

    // Compute ETag from last message order and count
    const lastOrder = (result?.data?.messages || []).reduce((max, m) => Math.max(max, m.order || 0), 0);
    const etag = `W/"msgs-${channelId}-${lastOrder}-${result?.data?.pagination?.count || 0}"`;
    const ifNoneMatch = event.headers?.['If-None-Match'] || event.headers?.['if-none-match'];
    if (ifNoneMatch && ifNoneMatch === etag) {
      return apiResponse(304, { header: { ETag: etag }, body: '' });
    }

    return apiResponse(200, { header: { ETag: etag }, body: result });

  } catch (error) {
    console.error('Error in getChannelMessages handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
