import { getSelfChannels } from '../useCases/getSelfChannels.js';
import { ValidationError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    // Extract params for filtering and pagination
    const queryParams = event.queryStringParameters || {};
    const {
      type,
      limit,
      offset,
      sortBy,
      sortOrder
    } = queryParams;

    // Get channels using use case
    const result = await getSelfChannels({
      userId,
      role,
      type,
      limit,
      offset,
      sortBy,
      sortOrder
    });

    return apiResponse(200, { body: result });

  } catch (error) {
    console.error('Error in getSelfChannels handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    return apiResponse(500, { 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
