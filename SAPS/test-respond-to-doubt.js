#!/usr/bin/env node

/**
 * Simple test script to verify the respondToDoubt fix
 * This script simulates the API call flow without making actual HTTP requests
 */

import { respondToDoubtService } from './src/application/services/video/respondToDoubtService.js';

// Mock the dependencies
const mockVideoRepository = {
  getCommentInfo: async (commentId) => {
    console.log(`Mock: Getting comment info for ${commentId}`);
    return {
      id: commentId,
      content: "Esta é uma dúvida sobre matemática",
      userId: "student123",
      username: "<PERSON>",
      videoId: 456,
      isDoubt: true,
      video: {
        id: 456,
        title: "Introdução à Álgebra"
      }
    };
  }
};

// Mock the ApiCaller
class MockApiCaller {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    console.log(`Mock: Created ApiCaller for ${baseUrl}`);
  }

  async post(endpoint, data, headers, token) {
    console.log(`Mock: POST ${this.baseUrl}${endpoint}`);
    console.log(`Mock: Data:`, JSON.stringify(data, null, 2));
    console.log(`Mock: Token: ${token ? 'Present' : 'Missing'}`);

    // Simulate successful channel creation with correct CDS API response structure
    return {
      data: {
        success: true,
        message: 'Channel created successfully',
        data: {
          _id: 'channel_' + Date.now(),
          name: data.name,
          members: data.members,
          type: data.type,
          contextType: data.contextType,
          contextId: data.contextId,
          ownerId: data.members[0],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
    };
  }
}

// Mock environment
process.env.CDS_API_URL = 'https://mock-cds-api.com';

// Test function
async function testRespondToDoubt() {
  console.log('🧪 Testing respondToDoubt service...\n');

  try {
    // Mock the imports by replacing them in the global scope
    global.videoRepository = mockVideoRepository;
    global.ApiCaller = MockApiCaller;

    const result = await respondToDoubtService({
      commentId: 123,
      teacherId: 'teacher456',
      token: 'Bearer mock-token'
    });

    console.log('✅ Test passed! Result:');
    console.log(JSON.stringify(result, null, 2));
    
    // Verify the result structure
    if (result.success && result.data.channelId && result.data.commentId === 123) {
      console.log('\n🎉 All assertions passed!');
      console.log('- Service returns success: ✓');
      console.log('- Channel ID is present: ✓');
      console.log('- Comment ID matches: ✓');
      console.log('- No circular dependency: ✓');
    } else {
      console.log('\n❌ Some assertions failed');
    }

  } catch (error) {
    console.error('❌ Test failed with error:');
    console.error(error.message);
    console.error(error.stack);
  }
}

// Run the test
testRespondToDoubt();
